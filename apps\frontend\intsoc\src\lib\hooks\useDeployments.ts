import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { Deployment } from '@telesoft/types';
import {
  deploymentsService,
  DeploymentsWebSocketMessage,
} from '../services/deployments';
import { ApiError } from '../api-client';
import { useAppConfig } from '../contexts/config-provider';
import { transformDeploymentData, ChartDataset } from '@telesoft/utils';

interface DeploymentTransformOptions {
  maxPoints?: number;
  minIntervalMs?: number;
}

export interface UseDeploymentsResult {
  deployments: Deployment[];
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

/**
 * Hook for managing deployments data with loading and error states
 */
export function useDeployments(): UseDeploymentsResult {
  const [deployments, setDeployments] = useState<Deployment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDeployments = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await deploymentsService.getDeployments();
      setDeployments(data);
    } catch (err) {
      let errorMessage = 'Failed to load deployments data';

      if (err instanceof ApiError) {
        errorMessage = `API Error: ${err.message}`;
      } else if (err instanceof Error) {
        // Handle specific backend unavailability messages
        if (
          err.message.includes('Backend Unavailable') ||
          err.message.includes('Service Unavailable') ||
          err.message.includes('503')
        ) {
          errorMessage =
            'Backend service is temporarily unavailable. Please try again later.';
        } else if (
          err.message.includes('Failed to connect') ||
          err.message.includes('Connection refused') ||
          err.message.includes('ECONNREFUSED')
        ) {
          errorMessage =
            'Unable to connect to the backend service. Please check your connection and try again.';
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
      console.error('Error fetching deployments:', err);

      // Don't clear existing deployments data on error - keep showing cached data
      // setDeployments([]); // Commented out to preserve existing data
    } finally {
      setLoading(false);
    }
  }, []);

  const refresh = useCallback(async () => {
    await fetchDeployments();
  }, [fetchDeployments]);

  useEffect(() => {
    fetchDeployments();
  }, [fetchDeployments]);

  return {
    deployments,
    loading,
    error,
    refresh,
  };
}

export interface UseDeploymentFiltersResult {
  filteredDeployments: Deployment[];
  filters: DeploymentFilters;
  setFilters: (filters: Partial<DeploymentFilters>) => void;
  resetFilters: () => void;
}

export interface DeploymentFilters {
  name?: string;
  namespace?: string;
  namePattern?: string; // For regex or partial matching
}

/**
 * Hook for filtering deployments with various criteria
 */
export function useDeploymentFilters(
  deployments: Deployment[],
): UseDeploymentFiltersResult {
  const [filters, setFiltersState] = useState<DeploymentFilters>({});

  const filteredDeployments = deployments.filter((deployment) => {
    if (filters.name && deployment.name !== filters.name) return false;
    if (filters.namespace && deployment.namespace !== filters.namespace)
      return false;
    if (filters.namePattern) {
      const pattern = new RegExp(filters.namePattern, 'i');
      if (!pattern.test(deployment.name)) return false;
    }
    return true;
  });

  const setFilters = useCallback((newFilters: Partial<DeploymentFilters>) => {
    setFiltersState((prev) => ({ ...prev, ...newFilters }));
  }, []);

  const resetFilters = useCallback(() => {
    setFiltersState({});
  }, []);

  return {
    filteredDeployments,
    filters,
    setFilters,
    resetFilters,
  };
}

/**
 * Hook for getting deployment statistics and distributions
 */
export function useDeploymentStats(deployments: Deployment[]) {
  return useMemo(() => {
    const namespaces = deploymentsService.getNamespaces(deployments);
    const deploymentNames = deploymentsService.getDeploymentNames(deployments);

    // Calculate aggregate statistics
    const stats = deployments.map((deployment) =>
      deploymentsService.getDeploymentStats(deployment),
    );
    const totalDataPoints = stats.reduce((sum, stat) => sum + stat.count, 0);
    const overallMin =
      stats.length > 0 ? Math.min(...stats.map((s) => s.min)) : 0;
    const overallMax =
      stats.length > 0 ? Math.max(...stats.map((s) => s.max)) : 0;
    const overallSum = stats.reduce((sum, stat) => sum + stat.sum, 0);
    const overallAvg = totalDataPoints > 0 ? overallSum / totalDataPoints : 0;

    // Get latest data points for each deployment
    const latestDataPoints = deployments
      .map((deployment) => ({
        deployment,
        latest: deploymentsService.getLatestDataPoint(deployment),
      }))
      .filter((item) => item.latest !== null)
      .sort((a, b) => (b.latest?.timestamp || 0) - (a.latest?.timestamp || 0));

    return {
      total: deployments.length,
      namespaces: namespaces,
      namespacesCount: namespaces.length,
      deploymentNames: deploymentNames,
      totalDataPoints,
      overallStats: {
        min: overallMin,
        max: overallMax,
        avg: overallAvg,
        sum: overallSum,
        count: totalDataPoints,
      },
      latestDataPoints,
      namespaceDistribution: namespaces.reduce(
        (acc, namespace) => {
          acc[namespace] = deployments.filter(
            (d) => d.namespace === namespace,
          ).length;
          return acc;
        },
        {} as Record<string, number>,
      ),
    };
  }, [deployments]);
}

export interface UseDeploymentsWebSocketOptions {
  autoConnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  fallbackToRest?: boolean;
}

export interface UseDeploymentsWebSocketResult {
  deployments: Deployment[];
  chartData: Record<string, ChartDataset[]>;
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  connect: () => void;
  disconnect: () => void;
  lastUpdate: string | null;
  refetch: () => Promise<void>;
}

/**
 * Hook for real-time deployments data using WebSocket with REST API fallback
 */
export function useDeploymentsWebSocket(
  options: UseDeploymentsWebSocketOptions = {},
): UseDeploymentsWebSocketResult {
  const { autoConnect = true, fallbackToRest = true } = options;

  // Get configuration state
  const { config, loading: configLoading } = useAppConfig();

  const [deployments, setDeployments] = useState<Deployment[]>([]);
  const [chartData, setChartData] = useState<Record<string, ChartDataset[]>>({});
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);

  const wsRef = useRef<WebSocket | null>(null);
  const subscriptionIdRef = useRef<string | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const isConnectingRef = useRef(false);
  const lastPongRef = useRef<number>(Date.now());
  const pingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Fallback REST API fetch
  const refetch = useCallback(async () => {
    if (!fallbackToRest) return;

    try {
      const data = await deploymentsService.getDeployments();
      setDeployments(data);
      setLastUpdate(new Date().toISOString());
      setError(null);
    } catch (err) {
      let errorMessage = 'Failed to fetch deployments';

      if (err instanceof ApiError) {
        errorMessage = `API Error: ${err.message}`;
      } else if (err instanceof Error) {
        // Handle specific backend unavailability messages
        if (
          err.message.includes('Backend Unavailable') ||
          err.message.includes('Service Unavailable') ||
          err.message.includes('503')
        ) {
          errorMessage = 'Backend service is temporarily unavailable';
        } else if (
          err.message.includes('Failed to connect') ||
          err.message.includes('Connection refused') ||
          err.message.includes('ECONNREFUSED')
        ) {
          errorMessage = 'Unable to connect to backend service';
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
      console.error('Failed to fetch deployments via REST API:', err);

      // Don't clear existing data on error
      // setDeployments([]); // Commented out to preserve existing data
    }
  }, [fallbackToRest]);

  const cleanup = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current);
      pingIntervalRef.current = null;
    }

    // Disconnect our specific subscription
    if (subscriptionIdRef.current) {
      deploymentsService.disconnectSubscriber(subscriptionIdRef.current);
      subscriptionIdRef.current = null;
    }

    wsRef.current = null;
    setIsConnected(false);
    setIsConnecting(false);
    isConnectingRef.current = false;
  }, []);

  // Update handleWebSocketMessage to track pongs
  const handleWebSocketMessage = useCallback(
    (message: DeploymentsWebSocketMessage) => {
      if (message.type === 'pong') {
        lastPongRef.current = Date.now();
        return;
      }

      if (message.type === 'ml-initial' && message.data?.deployments) {
        // Initial full data load - replace everything
        console.log('WebSocket: Received ml-initial', {
          deploymentsCount: message.data.deployments.length,
          timestamp: message.timestamp,
        });
        setDeployments([...message.data.deployments]);
        
        // Transform data for charts
        const transformedChartData = transformDeploymentData(message.data.deployments, {
          maxPoints: 50
        });
        setChartData(transformedChartData);
        
        setLastUpdate(message.timestamp);
      } else if (
        message.type === 'ml-update' &&
        message.data &&
        Array.isArray(message.data.deployments)
      ) {
        // Update deployments data - handle both append and clear actions
        const newDeployments = message.data.deployments;
        console.log('WebSocket: Received ml-update', {
          newDeploymentsCount: newDeployments.length,
          timestamp: message.timestamp,
          action: message.data.action,
        });
        if (message.data.action === 'clear' || newDeployments.length === 0) {
          // Clear action or empty array - replace all deployments
          console.log(
            'WebSocket: Clearing deployments (deletion or empty state)',
          );
          setDeployments([...newDeployments]); // Create new array reference
          setLastUpdate(message.timestamp);
        } else if (message.data.action === 'update') {
          // Update action - replace all deployments with new data
          console.log('WebSocket: Updating deployments with new data');
          setDeployments([...newDeployments]); // Create new array reference
          setLastUpdate(message.timestamp);
        } else if (
          message.data.action === 'append' &&
          newDeployments.length > 0
        ) {
          setDeployments((prev) => {
            // Create a map for merging deployments based on name + namespace
            const deploymentsMap = new Map<string, Deployment>();

            // Add existing deployments to map
            prev.forEach((deployment) => {
              const key = `${deployment.name}:${deployment.namespace}`;
              deploymentsMap.set(key, deployment);
            });

            // Merge new deployments with existing ones
            newDeployments.forEach((newDeployment) => {
              const key = `${newDeployment.name}:${newDeployment.namespace}`;
              const existingDeployment = deploymentsMap.get(key);

              if (existingDeployment) {
                // Merge data objects (timestamps and values)
                const mergedData = {
                  ...existingDeployment.data,
                  ...newDeployment.data,
                };

                // Update the deployment with merged data
                deploymentsMap.set(key, {
                  ...existingDeployment,
                  data: mergedData,
                });
              } else {
                // New deployment, add it to the map
                deploymentsMap.set(key, { ...newDeployment });
              }
            });

            const updatedDeployments = Array.from(deploymentsMap.values());

            console.log('WebSocket: Merged deployment data', {
              previousCount: prev.length,
              newCount: updatedDeployments.length,
              updatedDeployments: newDeployments.map(
                (d) => `${d.name}:${d.namespace}`,
              ),
            });

            return updatedDeployments;
          });
        }

        setLastUpdate(message.timestamp);
      } else if (message.type === 'ping') {
        // Respond to ping with pong
        const pongMessage: DeploymentsWebSocketMessage = {
          type: 'pong',
          timestamp: new Date().toISOString(),
        };
        deploymentsService.sendWebSocketMessage(pongMessage);
      }
    },
    [],
  );

  const connect = useCallback(async () => {
    // Don't check if already connected - we need to subscribe even to existing connections
    // The service will handle connection reuse automatically

    if (isConnectingRef.current) {
      return; // Connection in progress
    }

    // If we already have a subscription, don't create another one
    if (subscriptionIdRef.current) {
      console.log('Already have active subscription, skipping connect');
      return;
    }

    try {
      setIsConnecting(true);
      isConnectingRef.current = true;
      setError(null);

      const { ws, subscriptionId } = await deploymentsService.connectWebSocket(
        handleWebSocketMessage,
        (_error) => {
          setError('WebSocket connection error');
          setIsConnecting(false);
          isConnectingRef.current = false;
        },
        (_event) => {
          setIsConnected(false);
          setIsConnecting(false);
          isConnectingRef.current = false;

          console.log(
            'Deployments WebSocket disconnected, service will handle reconnection',
          );

          // Fallback to REST API if configured
          if (fallbackToRest) {
            console.log('Falling back to REST API...');
            refetch();
          }
        },
        () => {
          setIsConnected(true);
          setIsConnecting(false);
          isConnectingRef.current = false;
          reconnectAttemptsRef.current = 0;
          setError(null);

          // Setup heartbeat ping (only if we don't already have one)
          if (!pingIntervalRef.current) {
            pingIntervalRef.current = setInterval(() => {
              if (deploymentsService.isWebSocketConnected()) {
                const pingMessage: DeploymentsWebSocketMessage = {
                  type: 'ping',
                  timestamp: new Date().toISOString(),
                };
                deploymentsService.sendWebSocketMessage(pingMessage);

                // Check if we've received a pong recently
                if (Date.now() - lastPongRef.current > 30000) {
                  // 30 seconds without pong
                  console.warn(
                    'No pong received in 30 seconds, connection may be stale',
                  );
                  setError('Connection may be unstable');
                }
              }
            }, 10000); // Ping every 10 seconds
          }
        },
      );

      wsRef.current = ws;
      // Store subscription ID for cleanup
      subscriptionIdRef.current = subscriptionId;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to connect');
      setIsConnecting(false);
      isConnectingRef.current = false;

      // Fallback to REST API if configured
      if (fallbackToRest) {
        console.log('WebSocket connection failed, falling back to REST API...');
        refetch();
      }
    }
  }, [handleWebSocketMessage, refetch, fallbackToRest]);

  const disconnect = useCallback(() => {
    cleanup();
  }, [cleanup]);

  // Auto-connect when config is loaded and autoConnect is enabled
  useEffect(() => {
    if (!configLoading && autoConnect && config) {
      connect();
    }

    return () => {
      cleanup();
    };
  }, [configLoading, autoConnect, config, connect, cleanup]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  return {
    deployments,
    chartData,
    isConnected,
    isConnecting,
    error,
    connect,
    disconnect,
    lastUpdate,
    refetch,
  };
}

/**
 * Hook for time-based deployment data analysis
 */
export function useDeploymentTimeAnalysis(deployments: Deployment[]) {
  return useMemo(() => {
    const allTimestamps = new Set<number>();
    const timeRanges: { [key: string]: { start: number; end: number } } = {};

    deployments.forEach((deployment) => {
      const timestamps = Object.keys(deployment.data).map((ts) => parseInt(ts));
      timestamps.forEach((ts) => allTimestamps.add(ts));

      if (timestamps.length > 0) {
        const sortedTimestamps = timestamps.sort((a, b) => a - b);
        timeRanges[`${deployment.name}:${deployment.namespace}`] = {
          start: sortedTimestamps[0],
          end: sortedTimestamps[sortedTimestamps.length - 1],
        };
      }
    });

    const allTimestampsArray = Array.from(allTimestamps).sort((a, b) => a - b);
    const globalTimeRange =
      allTimestampsArray.length > 0
        ? {
            start: allTimestampsArray[0],
            end: allTimestampsArray[allTimestampsArray.length - 1],
            duration:
              allTimestampsArray[allTimestampsArray.length - 1] -
              allTimestampsArray[0],
          }
        : null;

    return {
      allTimestamps: allTimestampsArray,
      globalTimeRange,
      deploymentTimeRanges: timeRanges,
      getDeploymentDataInRange: (
        deploymentKey: string,
        start: number,
        end: number,
      ) => {
        const deployment = deployments.find(
          (d) => `${d.name}:${d.namespace}` === deploymentKey,
        );
        return deployment
          ? deploymentsService.getDataInTimeRange(deployment, start, end)
          : {};
      },
    };
  }, [deployments]);
}

export interface UseDeploymentFiltersConfigResult {
  filtersConfig: any;
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

/**
 * Hook for managing deployment filters configuration
 */
export function useDeploymentFiltersConfig(): UseDeploymentFiltersConfigResult {
  const [filtersConfig, setFiltersConfig] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchFiltersConfig = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await deploymentsService.getDeploymentFilters();
      setFiltersConfig(data);
    } catch (err) {
      let errorMessage = 'Failed to load deployment filters configuration';

      if (err instanceof ApiError) {
        errorMessage = `API Error: ${err.message}`;
      } else if (err instanceof Error) {
        // Handle specific backend unavailability messages
        if (
          err.message.includes('Backend Unavailable') ||
          err.message.includes('Service Unavailable') ||
          err.message.includes('503')
        ) {
          errorMessage =
            'Filter configuration service is temporarily unavailable. Please try again later.';
        } else if (
          err.message.includes('Failed to connect') ||
          err.message.includes('Connection refused') ||
          err.message.includes('ECONNREFUSED')
        ) {
          errorMessage =
            'Unable to connect to the filter configuration service. Please check your connection and try again.';
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
      console.error('Error fetching deployment filters configuration:', err);

      // Don't clear existing configuration data on error - keep showing cached data
      // setFiltersConfig({}); // Commented out to preserve existing data
    } finally {
      setLoading(false);
    }
  }, []);

  const refresh = useCallback(async () => {
    await fetchFiltersConfig();
  }, [fetchFiltersConfig]);

  useEffect(() => {
    fetchFiltersConfig();
  }, [fetchFiltersConfig]);

  return {
    filtersConfig,
    loading,
    error,
    refresh,
  };
}

export interface UseDeploymentDestinationsConfigResult {
  destinationsConfig: any;
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

/**
 * Hook for managing deployment destinations configuration
 */
export function useDeploymentDestinationsConfig(): UseDeploymentDestinationsConfigResult {
  const [destinationsConfig, setDestinationsConfig] = useState<any>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDestinationsConfig = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await deploymentsService.getDeploymentDestinations();
      setDestinationsConfig(data);
    } catch (err) {
      let errorMessage = 'Failed to load deployment destinations configuration';

      if (err instanceof ApiError) {
        errorMessage = `API Error: ${err.message}`;
      } else if (err instanceof Error) {
        // Handle specific backend unavailability messages
        if (
          err.message.includes('Backend Unavailable') ||
          err.message.includes('Service Unavailable') ||
          err.message.includes('503')
        ) {
          errorMessage =
            'Destinations configuration service is temporarily unavailable. Please try again later.';
        } else if (
          err.message.includes('Failed to connect') ||
          err.message.includes('Connection refused') ||
          err.message.includes('ECONNREFUSED')
        ) {
          errorMessage =
            'Unable to connect to the destinations configuration service. Please check your connection and try again.';
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
      console.error('Error fetching deployment destinations configuration:', err);

      // Don't clear existing configuration data on error - keep showing cached data
      // setDestinationsConfig([]); // Commented out to preserve existing data
    } finally {
      setLoading(false);
    }
  }, []);

  const refresh = useCallback(async () => {
    await fetchDestinationsConfig();
  }, [fetchDestinationsConfig]);

  useEffect(() => {
    fetchDestinationsConfig();
  }, [fetchDestinationsConfig]);

  return {
    destinationsConfig,
    loading,
    error,
    refresh,
  };
}



