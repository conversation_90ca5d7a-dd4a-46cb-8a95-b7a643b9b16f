import { useState, useEffect } from 'react';
import { ThreatHunt, SourceTypeCount } from '@telesoft/types';
import { threatHuntingService } from '../services/threatHunting';
 
export interface ThreatHuntingData {
    threatHunts: ThreatHunt[];
    sourceTypeCounts: SourceTypeCount[];
    loading: boolean;
    error: string | null;
}
 
export function useThreatHunting() {
    const [data, setData] = useState<ThreatHuntingData>({
        threatHunts: [],
        sourceTypeCounts: [],
        loading: true,
        error: null,
    });
 
    const fetchThreatHunts = async () => {
        try {
            setData(prev => ({ ...prev, loading: true, error: null }));
 
            let threatHunts = await threatHuntingService.getThreatHunts();
            if (!Array.isArray(threatHunts)) {
                threatHunts = [];
            }
            const sourceTypeCounts = threatHuntingService.getSourceTypeDistribution(threatHunts);
 
            setData({
                threatHunts,
                sourceTypeCounts,
                loading: false,
                error: null,
            });
        } catch (error) {
            console.error('Failed to fetch threat hunts:', error);
            setData(prev => ({
                ...prev,
                loading: false,
                error: error instanceof Error ? error.message : 'Failed to fetch threat hunts',
                threatHunts: [],
                sourceTypeCounts: [],
            }));
        }
    };
 
    useEffect(() => {
        fetchThreatHunts();
    }, []);
 
    const refresh = () => {
        fetchThreatHunts();
    };
 
    return {
        ...data,
        refresh,
    };
}